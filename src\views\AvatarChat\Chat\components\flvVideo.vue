<template>
  <div class="flv-player-container">
    <!-- 加载状态覆盖层 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载直播流...</p>
    </div>

    <!-- 错误状态覆盖层 -->
    <div v-if="!isSupported && !loading" class="error-overlay">
      <p>当前浏览器不支持 FLV 直播流播放</p>
      <p>请使用 Chrome、Firefox 或 Edge 浏览器</p>
    </div>

    <!-- 视频元素始终存在，通过样式控制显示 -->
    <video
      id="live_id"
      ref="videoRef"
      autoplay
      :muted="props.muted"
      playsinline
      :style="{
        width: '100%',
        maxHeight: '100%',
        visibility: (loading || !isSupported) ? 'hidden' : 'visible'
      }"
    ></video>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';

  declare global {
    interface Window {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      flvjs: any;
    }
  }

  const props = defineProps({
    src: {
      type: String,
      required: true,
    },
    muted: {
      type: Boolean,
      default: true,
    },
    debug: {
      type: Boolean,
      default: false,
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let player: any = null;
  const isSupported = ref(true);
  const loading = ref(true);
  const videoRef = ref<HTMLVideoElement | null>(null);

  // 重新播放函数
  const replay = () => {
    if (player) {
      player
        .play()
        .then(() => {
          console.log('flv.js 重新播放成功');
        })
        .catch((err: string) => {
          console.error('flv.js 重新播放失败:', err);
          // 短暂延迟后尝试再次播放
          setTimeout(replay, 3000);
        });
    }
  };

  // 等待 flv.js 加载完成
  const waitForFlvjs = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.flvjs) {
        resolve();
        return;
      }

      let attempts = 0;
      const maxAttempts = 10; // 最多等待5秒
      const checkInterval = setInterval(() => {
        attempts++;
        if (window.flvjs) {
          clearInterval(checkInterval);
          resolve();
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          reject(new Error('flv.js 加载超时'));
        }
      }, 100);
    });
  };

  onMounted(async () => {
    try {
      console.log('FlvVideo 组件挂载，接收到的 src:', props.src);

      // 等待 flv.js 加载完成
      await waitForFlvjs();
      console.log('FLV.js loaded: 检查 FLV.js 是否加载成功', window.flvjs);

      // 检查浏览器支持
      if (!window.flvjs || !window.flvjs.isSupported()) {
        console.log('当前浏览器不支持 FLV.js');
        isSupported.value = false;
        loading.value = false;
        return;
      }

      // 等待视频元素渲染完成
      await nextTick();

      // 优先使用 ref，如果没有则使用 getElementById
      let videoElement = videoRef.value;
      if (!videoElement) {
        videoElement = document.getElementById('live_id') as HTMLVideoElement;
      }

      if (!videoElement) {
        console.log('未找到视频元素，videoRef:', videoRef.value, 'getElementById:', document.getElementById('live_id'));
        loading.value = false;
        return;
      }

      console.log('找到视频元素:', videoElement);

      const flvjs = window.flvjs;

      if (props.debug) {
        flvjs.LoggingControl.enableDebug = true;
      }

      player = flvjs.createPlayer({
        type: 'flv',
        url: props.src,
        isLive: true,
      }, {
        enableWorker: false, // 禁用 Web Worker，避免一些兼容性问题
        enableStashBuffer: false, // 禁用缓存缓冲区，减少延迟
        autoCleanupSourceBuffer: true, // 自动清理源缓冲区
        autoCleanupMaxBackwardDuration: 3, // 保留3秒的回放缓冲
        autoCleanupMinBackwardDuration: 2, // 最小保留2秒
        fixAudioTimestampGap: true, // 修复音频时间戳间隙
        accurateSeek: false, // 禁用精确寻址，提高性能
        seekType: 'range', // 使用范围寻址
        seekParamStart: 'bstart',
        seekParamEnd: 'bend',
        rangeLoadZeroStart: false,
        lazyLoad: true, // 启用懒加载
        lazyLoadMaxDuration: 3 * 60, // 最大懒加载3分钟
        lazyLoadRecoverDuration: 30, // 恢复加载30秒
        deferLoadAfterSourceOpen: false,
        reuseRedirectedURL: false,
      });

      player.attachMediaElement(videoElement);

      // 添加播放器事件监听
      player.on(flvjs.Events.ERROR, (errorType: string, errorDetail: string) => {
        console.error('FLV播放器错误:', errorType, errorDetail);
        if (errorType === flvjs.ErrorTypes.NETWORK_ERROR) {
          console.log('网络错误，尝试重新连接...');
          setTimeout(() => {
            if (player) {
              player.unload();
              player.load();
            }
          }, 2000);
        }
      });

      player.on(flvjs.Events.LOADING_COMPLETE, () => {
        console.log('FLV流加载完成');
      });

      player.on(flvjs.Events.RECOVERED_EARLY_EOF, () => {
        console.log('从早期EOF恢复');
      });

      player.load();

      // 设置加载完成状态
      loading.value = false;

      player
        .play()
        .then(() => {
          console.log('flv.js 播放成功');
        })
        .catch((err: string) => {
          console.error('flv.js 播放失败:', err);
          isSupported.value = false;
          replay(); // 调用重新播放函数
        });
    } catch (error) {
      console.error('FLV.js 初始化失败:', error);
      isSupported.value = false;
      loading.value = false;
    }
  });

  onBeforeUnmount(() => {
    if (player) {
      player.destroy();
      player = null;
    }
    if (videoRef.value) {
      videoRef.value.removeEventListener('ended', replay);
    }
  });
</script>

<style scoped>
.flv-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  text-align: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-overlay p {
  margin: 8px 0;
  color: #ff4d4f;
}
</style>
