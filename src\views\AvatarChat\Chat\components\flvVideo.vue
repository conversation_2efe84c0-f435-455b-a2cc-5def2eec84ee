<template>
  <div class="flv-player-container">
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>正在加载直播流...</p>
    </div>
    <div v-else-if="!isSupported" class="error-container">
      <p>当前浏览器不支持 FLV 直播流播放</p>
      <p>请使用 Chrome、Firefox 或 Edge 浏览器</p>
    </div>
    <video
      v-else
      id="live_id"
      ref="videoRef"
      autoplay
      :muted="props.muted"
      playsinline
      style="width: 100%; max-height: 100%"
    ></video>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';

  declare global {
    interface Window {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      flvjs: any;
    }
  }

  const props = defineProps({
    src: {
      type: String,
      required: true,
    },
    muted: {
      type: Boolean,
      default: true,
    },
    debug: {
      type: Boolean,
      default: false,
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let player: any = null;
  const isSupported = ref(true);
  const loading = ref(true);
  const videoRef = ref<HTMLVideoElement | null>(null);

  // 重新播放函数
  const replay = () => {
    if (player && videoRef.value) {
      player
        .play()
        .then(() => {
          console.log('flv.js 重新播放成功');
        })
        .catch((err: string) => {
          console.error('flv.js 重新播放失败:', err);
          // 短暂延迟后尝试再次播放
          setTimeout(replay, 3000);
        });
    }
  };

  // 等待 flv.js 加载完成
  const waitForFlvjs = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.flvjs) {
        resolve();
        return;
      }

      let attempts = 0;
      const maxAttempts = 50; // 最多等待5秒
      const checkInterval = setInterval(() => {
        attempts++;
        if (window.flvjs) {
          clearInterval(checkInterval);
          resolve();
        } else if (attempts >= maxAttempts) {
          clearInterval(checkInterval);
          reject(new Error('flv.js 加载超时'));
        }
      }, 100);
    });
  };

  onMounted(async () => {
    try {
      // 等待 flv.js 加载完成
      await waitForFlvjs();
      console.log('FLV.js loaded: 检查 FLV.js 是否加载成功', window.flvjs);

      // 检查浏览器支持
      if (!window.flvjs || !window.flvjs.isSupported()) {
        console.log('当前浏览器不支持 FLV.js');
        isSupported.value = false;
        loading.value = false;
        return;
      }

      const videoElement = document.getElementById('live_id') as HTMLVideoElement;
      if (!videoElement) {
        console.log('未找到视频元素');
        loading.value = false;
        return;
      }

      await nextTick();

      const flvjs = window.flvjs;

      if (props.debug) {
        flvjs.LoggingControl.enableDebug = true;
      }

      player = flvjs.createPlayer({
        type: 'flv',
        url: props.src,
        isLive: true,
      });

      player.attachMediaElement(videoElement);
      player.load();

      // 设置加载完成状态
      loading.value = false;

      player
        .play()
        .then(() => {
          console.log('flv.js 播放成功');
        })
        .catch((err: string) => {
          console.error('flv.js 播放失败:', err);
          isSupported.value = false;
          replay(); // 调用重新播放函数
        });
    } catch (error) {
      console.error('FLV.js 初始化失败:', error);
      isSupported.value = false;
      loading.value = false;
    }
  });

  onBeforeUnmount(() => {
    if (player) {
      player.destroy();
      player = null;
    }
    if (videoRef.value) {
      videoRef.value.removeEventListener('ended', replay);
    }
  });
</script>

<style scoped>
.flv-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #fff;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container p {
  margin: 8px 0;
  color: #ff4d4f;
}
</style>
